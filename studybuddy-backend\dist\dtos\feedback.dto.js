"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FeedbackFilterDto = exports.UpdateFeedbackStatusDto = exports.CreateFeedbackDto = void 0;
const class_validator_1 = require("class-validator");
const feedback_schema_1 = require("../schemas/feedback.schema");
class CreateFeedbackDto {
}
exports.CreateFeedbackDto = CreateFeedbackDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateFeedbackDto.prototype, "title", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateFeedbackDto.prototype, "description", void 0);
class UpdateFeedbackStatusDto {
}
exports.UpdateFeedbackStatusDto = UpdateFeedbackStatusDto;
__decorate([
    (0, class_validator_1.IsEnum)(feedback_schema_1.FeedbackStatus),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UpdateFeedbackStatusDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateFeedbackStatusDto.prototype, "adminResponse", void 0);
class FeedbackFilterDto {
}
exports.FeedbackFilterDto = FeedbackFilterDto;
__decorate([
    (0, class_validator_1.IsEnum)(feedback_schema_1.FeedbackStatus),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FeedbackFilterDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsMongoId)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], FeedbackFilterDto.prototype, "userId", void 0);
//# sourceMappingURL=feedback.dto.js.map