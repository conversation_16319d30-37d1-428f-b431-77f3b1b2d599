import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export enum FeedbackStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  REJECTED = 'rejected'
}

@Schema({ timestamps: true })
export class Feedback extends Document {
  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true, type: MongooseSchema.Types.ObjectId })
  userId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  userName: string;

  @Prop({ 
    type: String, 
    enum: Object.values(FeedbackStatus),
    default: FeedbackStatus.PENDING 
  })
  status: FeedbackStatus;

  @Prop({ type: String, default: null })
  adminResponse: string;
}

export const FeedbackSchema = SchemaFactory.createForClass(Feedback);