"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FeedbackService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const feedback_schema_1 = require("../schemas/feedback.schema");
const users_service_1 = require("../users/users.service");
let FeedbackService = class FeedbackService {
    constructor(feedbackModel, usersService) {
        this.feedbackModel = feedbackModel;
        this.usersService = usersService;
    }
    async createFeedback(userId, createFeedbackDto) {
        const userDetails = await this.usersService.getUserDetailsByUserId(userId);
        console.log('userDetails:', userDetails);
        console.log('typeof userDetails:', typeof userDetails);
        if (!userDetails) {
            throw new common_1.NotFoundException('User not found');
        }
        const userName = typeof userDetails === 'string' ? userDetails : 'Anonymous User';
        const newFeedback = new this.feedbackModel({
            ...createFeedbackDto,
            userId,
            userName,
            status: feedback_schema_1.FeedbackStatus.PENDING
        });
        return newFeedback.save();
    }
    async getUserFeedbacks(userId) {
        return this.feedbackModel.find({ userId }).sort({ createdAt: -1 }).exec();
    }
    async getUserFeedbackById(userId, feedbackId) {
        const feedback = await this.feedbackModel.findById(feedbackId).exec();
        if (!feedback) {
            throw new common_1.NotFoundException(`Feedback with ID ${feedbackId} not found`);
        }
        if (feedback.userId.toString() !== userId) {
            throw new common_1.ForbiddenException('You do not have permission to access this feedback');
        }
        return feedback;
    }
    async getAllFeedbacks(filterDto) {
        const filter = {};
        if (filterDto.status) {
            filter.status = filterDto.status;
        }
        if (filterDto.userId) {
            filter.userId = filterDto.userId;
        }
        return this.feedbackModel.find(filter).sort({ createdAt: -1 }).exec();
    }
    async getFeedbackById(feedbackId) {
        const feedback = await this.feedbackModel.findById(feedbackId).exec();
        if (!feedback) {
            throw new common_1.NotFoundException(`Feedback with ID ${feedbackId} not found`);
        }
        return feedback;
    }
    async updateFeedbackStatus(feedbackId, updateFeedbackStatusDto) {
        const updatedFeedback = await this.feedbackModel.findByIdAndUpdate(feedbackId, updateFeedbackStatusDto, { new: true }).exec();
        if (!updatedFeedback) {
            throw new common_1.NotFoundException(`Feedback with ID ${feedbackId} not found`);
        }
        return updatedFeedback;
    }
    async deleteFeedback(feedbackId) {
        const result = await this.feedbackModel.findByIdAndDelete(feedbackId).exec();
        if (!result) {
            throw new common_1.NotFoundException(`Feedback with ID ${feedbackId} not found`);
        }
        return { success: true };
    }
};
exports.FeedbackService = FeedbackService;
exports.FeedbackService = FeedbackService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(feedback_schema_1.Feedback.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        users_service_1.UsersService])
], FeedbackService);
//# sourceMappingURL=feedback.service.js.map