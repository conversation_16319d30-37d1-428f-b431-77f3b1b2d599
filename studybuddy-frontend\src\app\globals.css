@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 251 100% 5%;
  --foreground: 0 0% 100%;
}

body {
  background-color: #090017;
  color: white;
  overflow-x: hidden;
}

.hero-glow {
  position: absolute;
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, rgba(64, 86, 255, 0.2) 0%, rgba(9, 0, 23, 0) 70%);
  border-radius: 50%;
  pointer-events: none;
}


@keyframes typingAnimation {
  0% { opacity: 0.3; transform: translateY(0); }
  50% { opacity: 1; transform: translateY(-3px); }
  100% { opacity: 0.3; transform: translateY(0); }
}

.dot {
  animation: typingAnimation 1.5s infinite ease-in-out;
}

.dot:nth-child(1) {
  animation-delay: 0s;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}


/* Custom scrollbar styles */
/* :root {
  --scrollbar-size: 4px;
  --scrollbar-thumb-color: rgba(255, 255, 255, 0.1);
}

.scrollarea-thumb-y {
  background: var(--scrollbar-thumb-color) !important;
  width: var(--scrollbar-size) !important;
}

.scrollarea-track-y {
  width: var(--scrollbar-size) !important;
} */


