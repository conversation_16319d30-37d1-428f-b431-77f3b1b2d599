"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminController = void 0;
const common_1 = require("@nestjs/common");
const admin_service_1 = require("./admin.service");
const admin_guard_1 = require("../guard/admin.guard");
const subject_dto_1 = require("../dtos/subject.dto");
const quiz_dto_1 = require("../dtos/quiz.dto");
let AdminController = class AdminController {
    constructor(adminService) {
        this.adminService = adminService;
    }
    async getAllSubjects() {
        return this.adminService.getAllSubjects();
    }
    async getSubjectById(id) {
        return this.adminService.getSubjectById(id);
    }
    async createSubject(subjectDto) {
        return this.adminService.createSubject(subjectDto);
    }
    async updateSubject(id, updateSubjectDto) {
        return this.adminService.updateSubject(id, updateSubjectDto);
    }
    async deleteSubject(id) {
        return this.adminService.deleteSubject(id);
    }
    async addTopic(addTopicDto) {
        return this.adminService.addTopic(addTopicDto);
    }
    async updateTopic(updateTopicDto) {
        return this.adminService.updateTopic(updateTopicDto);
    }
    async deleteTopic(subjectId, topicId) {
        return this.adminService.deleteTopic(subjectId, topicId);
    }
    async createQuiz(createQuizDto) {
        return this.adminService.createQuiz(createQuizDto);
    }
    async getAllQuizzes(filterDto) {
        return this.adminService.getAllQuizzes(filterDto);
    }
    async getQuizById(id) {
        return this.adminService.getQuizById(id);
    }
    async updateQuiz(id, updateQuizDto) {
        return this.adminService.updateQuiz(id, updateQuizDto);
    }
    async deleteQuiz(id) {
        return this.adminService.deleteQuiz(id);
    }
};
exports.AdminController = AdminController;
__decorate([
    (0, common_1.Get)('subjects'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getAllSubjects", null);
__decorate([
    (0, common_1.Get)('subjects/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getSubjectById", null);
__decorate([
    (0, common_1.Post)('subjects'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [subject_dto_1.SubjectDto]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "createSubject", null);
__decorate([
    (0, common_1.Put)('subjects/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, subject_dto_1.UpdateSubjectDto]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "updateSubject", null);
__decorate([
    (0, common_1.Delete)('subjects/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "deleteSubject", null);
__decorate([
    (0, common_1.Post)('topics'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [subject_dto_1.AddTopicDto]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "addTopic", null);
__decorate([
    (0, common_1.Put)('topics'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [subject_dto_1.UpdateTopicDto]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "updateTopic", null);
__decorate([
    (0, common_1.Delete)('topics'),
    __param(0, (0, common_1.Query)('subjectId')),
    __param(1, (0, common_1.Query)('topicId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "deleteTopic", null);
__decorate([
    (0, common_1.Post)('quizzes'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [quiz_dto_1.CreateQuizDto]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "createQuiz", null);
__decorate([
    (0, common_1.Get)('quizzes'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [quiz_dto_1.QuizFilterDto]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getAllQuizzes", null);
__decorate([
    (0, common_1.Get)('quizzes/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getQuizById", null);
__decorate([
    (0, common_1.Put)('quizzes/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, quiz_dto_1.UpdateQuizDto]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "updateQuiz", null);
__decorate([
    (0, common_1.Delete)('quizzes/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "deleteQuiz", null);
exports.AdminController = AdminController = __decorate([
    (0, common_1.Controller)('admin'),
    (0, common_1.UseGuards)(admin_guard_1.AdminGuard),
    __metadata("design:paramtypes", [admin_service_1.AdminService])
], AdminController);
//# sourceMappingURL=admin.controller.js.map